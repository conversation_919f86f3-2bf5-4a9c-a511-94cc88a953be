# Robot Framework + Java Web Automation

A hybrid web automation testing framework combining the simplicity of Robot Framework with the power of Java libraries using Jython integration.

## 🏗️ Architecture

```
Robot Framework Test Layer
    ↓ (calls)
Custom Java Keywords/Libraries  
    ↓ (uses)
Selenium WebDriver (Java)
    ↓ (controls)
Web Browser
```

## 📋 Prerequisites

- **Java 11+** - [Download from Adoptium](https://adoptium.net/)
- **Maven 3.6+** - [Download from Apache Maven](https://maven.apache.org/download.cgi)
- **Python 3.7+** - [Download from Python.org](https://www.python.org/downloads/)

## 🚀 Quick Setup

1. **Clone/Download this project**
2. **Run the setup script:**
   ```bash
   python setup.py
   ```
3. **Run sample tests:**
   ```bash
   robot tests/web_tests.robot
   ```

## 📁 Project Structure

```
selenium/
├── pom.xml                                 # Maven configuration
├── src/main/java/com/automation/
│   ├── keywords/WebAutomationLibrary.java # Custom Robot Framework keywords
│   ├── pages/LoginPage.java               # Page Object Model classes
│   └── utils/TestDataManager.java         # Utility classes
├── tests/
│   ├── web_tests.robot                     # Main test cases
│   └── keywords/web_keywords.robot        # Custom Robot Framework keywords
├── resources/
│   ├── variables.robot                     # Test variables and configuration
│   └── test_data/users.json              # Test data files
├── results/                               # Test results and screenshots
└── drivers/                               # WebDriver binaries (optional)
```

## 🔧 Key Features

### ✅ What You Get

- **Familiar Robot Framework syntax** - Same as your Appium experience
- **Java power when needed** - Complex logic, database operations, API calls
- **Automatic WebDriver management** - No manual driver downloads needed
- **Built-in screenshot capture** - Automatic failure screenshots
- **Page Object Model support** - Maintainable page classes
- **Test data management** - JSON and properties file support
- **Cross-browser testing** - Chrome, Firefox, Edge support
- **Advanced web actions** - JavaScript execution, window management

### 🆚 Comparison with Your Mobile Experience

| Aspect | Mobile (Appium) | Web (This Framework) |
|--------|----------------|---------------------|
| **Test Syntax** | Robot Framework | ✅ Same Robot Framework |
| **Locators** | ID, XPath, Accessibility ID | ID, Name, CSS, XPath, Class |
| **Actions** | Tap, Swipe, Scroll | Click, Type, Hover, Select |
| **Language** | Robot + Python | ✅ Robot + Java |
| **Custom Keywords** | Python libraries | ✅ Java libraries |

## 📖 Usage Examples

### Basic Test Case
```robot
*** Test Cases ***
Login Test
    Setup Browser              chrome
    Navigate To Url            https://example.com/login
    Enter Text                 id=username    testuser
    Enter Text                 id=password    password123
    Click Element              id=login-button
    Verify Element Text        css=.welcome    Welcome
    Close Browser
```

### Using Java Custom Keywords
```robot
*** Test Cases ***
Advanced Login Test
    Setup Test Environment     chrome    ${TEST_DATA_DIR}/users.json
    ${username}=               Get Test Data    valid_users.admin.username
    ${password}=               Get Test Data    valid_users.admin.password
    Perform Valid Login        ${username}    ${password}
    Verify Login Success
    Cleanup Test Environment
```

### Java Library Example
```java
@RobotKeyword
public void performComplexValidation(String data) {
    // Complex business logic in Java
    DatabaseConnection db = new DatabaseConnection();
    ValidationResult result = businessRuleEngine.validate(data);
    // ... more complex operations
}
```

## 🎯 Running Tests

### Basic Commands
```bash
# Run all tests
robot tests/web_tests.robot

# Run specific test
robot -t "TC001*" tests/web_tests.robot

# Run with different browser
robot -v BROWSER:firefox tests/web_tests.robot

# Run tests with tags
robot -i smoke tests/web_tests.robot

# Run with custom variables
robot -v BASE_URL:https://staging.example.com tests/web_tests.robot
```

### Advanced Options
```bash
# Parallel execution (if you have robotframework-pabot)
pabot --processes 4 tests/web_tests.robot

# Generate detailed reports
robot --outputdir results --log detailed_log.html tests/web_tests.robot

# Run with custom test data
robot -v TEST_DATA_FILE:custom_data.json tests/web_tests.robot
```

## 🔧 Configuration

### Browser Configuration
Edit `resources/variables.robot`:
```robot
${BROWSER}                chrome
${TIMEOUT}                10
${BASE_URL}               https://your-app.com
```

### Test Data Configuration
Edit `resources/test_data/users.json`:
```json
{
  "valid_users": {
    "admin": {
      "username": "<EMAIL>",
      "password": "your-password"
    }
  }
}
```

## 🧪 Writing Your Own Tests

### 1. Create a New Test File
```robot
*** Settings ***
Resource    keywords/web_keywords.robot

*** Test Cases ***
My Custom Test
    Setup Test Environment
    # Your test steps here
    Cleanup Test Environment
```

### 2. Add Custom Java Keywords
```java
public class MyCustomLibrary {
    @RobotKeyword
    public void myCustomAction(String parameter) {
        // Your Java logic here
    }
}
```

### 3. Use in Robot Framework
```robot
Library    com.automation.keywords.MyCustomLibrary

*** Test Cases ***
Test With Custom Keyword
    My Custom Action    test parameter
```

## 🐛 Troubleshooting

### Common Issues

1. **Java not found**
   - Install Java 11+ and ensure it's in PATH
   - Run `java -version` to verify

2. **Maven compilation fails**
   - Check internet connection for dependency downloads
   - Run `mvn clean compile` manually

3. **Robot Framework can't find Java libraries**
   - Ensure Maven compilation succeeded
   - Check CLASSPATH includes target/classes

4. **WebDriver issues**
   - WebDriverManager should handle drivers automatically
   - For manual setup, download drivers to `drivers/` folder

### Debug Mode
```bash
# Run with debug output
robot --loglevel DEBUG tests/web_tests.robot

# Run single test with maximum logging
robot --loglevel TRACE -t "TC001*" tests/web_tests.robot
```

## 📊 Reports and Results

After running tests, check the `results/` directory:
- `report.html` - Test execution report
- `log.html` - Detailed test log
- `output.xml` - Raw test results
- `screenshots/` - Captured screenshots

## 🔄 Next Steps

1. **Customize for your application:**
   - Update URLs in `variables.robot`
   - Modify locators for your app's elements
   - Add your specific test data

2. **Extend with Java libraries:**
   - Add database connectivity
   - Integrate with APIs
   - Implement complex business logic

3. **Scale your testing:**
   - Add more page objects
   - Implement data-driven testing
   - Set up CI/CD integration

## 🤝 Support

This framework gives you the best of both worlds - the familiar Robot Framework syntax you know from mobile testing, with the full power of Java for complex operations. Start with simple tests and gradually add more sophisticated Java-based functionality as needed!
