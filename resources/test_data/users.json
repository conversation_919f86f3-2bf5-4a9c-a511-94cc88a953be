{"valid_users": {"admin": {"username": "<EMAIL>", "password": "admin123", "role": "administrator", "permissions": ["read", "write", "delete"]}, "user1": {"username": "<EMAIL>", "password": "user123", "role": "user", "permissions": ["read"]}, "manager": {"username": "<EMAIL>", "password": "manager123", "role": "manager", "permissions": ["read", "write"]}}, "invalid_users": {"invalid_email": {"username": "invalid-email", "password": "password123"}, "wrong_password": {"username": "<EMAIL>", "password": "wrongpassword"}, "empty_fields": {"username": "", "password": ""}}, "test_data": {"app_name": "Test Application", "base_url": "https://example.com", "timeout": 30, "browser_options": ["chrome", "firefox", "edge"], "test_environment": "staging"}, "form_data": {"registration": {"first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "email": "<EMAIL>", "phone": "+1234567890", "address": {"street": "123 Main St", "city": "Anytown", "state": "CA", "zip": "12345"}}, "profile": {"bio": "This is a test bio for automation testing", "interests": ["technology", "automation", "testing"], "newsletter": true, "notifications": false}}}