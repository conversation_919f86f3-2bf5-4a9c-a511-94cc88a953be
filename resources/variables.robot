*** Variables ***
# Browser Configuration
${BROWSER}                chrome
${TIMEOUT}                10
${IMP<PERSON><PERSON><PERSON>_WAIT}          5

# Test URLs
${BASE_URL}               https://example.com
${LOGIN_URL}              ${BASE_URL}/login
${DASHBOARD_URL}          ${BASE_URL}/dashboard

# Test Data
${VALID_USERNAME}         <EMAIL>
${VALID_PASSWORD}         password123
${INVALID_USERNAME}       <EMAIL>
${INVALID_PASSWORD}       wrongpassword

# Locators
${USERNAME_FIELD}         id=username
${PASSWORD_FIELD}         id=password
${LOGIN_BUTTON}           id=login-button
${ERROR_MESSAGE}          css=.error-message
${WELCOME_MESSAGE}        css=.welcome-message
${LOGOUT_BUTTON}          id=logout-button

# File Paths
${TEST_DATA_DIR}          ${CURDIR}/test_data
${SCREENSHOTS_DIR}        ${CURDIR}/../results/screenshots
${DRIVERS_DIR}            ${CURDIR}/../drivers

# Expected Messages
${LOGIN_SUCCESS_MSG}      Welcome
${LOGIN_ERROR_MSG}        Invalid credentials
${REQUIRED_FIELD_MSG}     This field is required

# Test Environment
${ENVIRONMENT}            test
${API_BASE_URL}           https://api.example.com
${DATABASE_URL}           **********************************
