*** Settings ***
Documentation    Custom keywords combining Robot Framework with Java libraries
Library          com.automation.keywords.WebAutomationLibrary
Library          com.automation.utils.TestDataManager
Resource         ../../resources/variables.robot

*** Keywords ***
# Browser Management Keywords
Setup Browser
    [Documentation]    Initialize browser and navigate to base URL
    [Arguments]        ${browser_name}=${BROWSER}    ${url}=${BASE_URL}
    Initialize Browser    ${browser_name}
    Navigate To Url       ${url}
    Wait For Page Load

Teardown Browser
    [Documentation]    Close browser and cleanup
    Close Browser

# Login Related Keywords
Open Login Page
    [Documentation]    Navigate to login page and verify it's loaded
    Navigate To Url    ${LOGIN_URL}
    Wait For Page Load
    Element Should Be Visible    ${USERNAME_FIELD}
    Element Should Be Visible    ${PASSWORD_FIELD}
    Element Should Be Visible    ${LOGIN_BUTTON}

Perform Valid Login
    [Documentation]    Login with valid credentials
    [Arguments]        ${username}=${VALID_USERNAME}    ${password}=${VALID_PASSWORD}
    Enter Text         ${USERNAME_FIELD}    ${username}
    Enter Text         ${PASSWORD_FIELD}    ${password}
    Click Element      ${LOGIN_BUTTON}
    Wait For Page Load

Perform Invalid Login
    [Documentation]    Login with invalid credentials
    [Arguments]        ${username}=${INVALID_USERNAME}    ${password}=${INVALID_PASSWORD}
    Enter Text         ${USERNAME_FIELD}    ${username}
    Enter Text         ${PASSWORD_FIELD}    ${password}
    Click Element      ${LOGIN_BUTTON}

Verify Login Success
    [Documentation]    Verify successful login
    ${welcome_text}=   Get Element Text    ${WELCOME_MESSAGE}
    Should Contain     ${welcome_text}     ${LOGIN_SUCCESS_MSG}
    Log                Login successful: ${welcome_text}

Verify Login Failure
    [Documentation]    Verify failed login
    ${error_text}=     Get Element Text    ${ERROR_MESSAGE}
    Should Contain     ${error_text}       ${LOGIN_ERROR_MSG}
    Log                Login failed as expected: ${error_text}

# Data Management Keywords
Load Test Data From JSON
    [Documentation]    Load test data from JSON file
    [Arguments]        ${file_path}
    Load Json Test Data    ${file_path}
    Log                Test data loaded from: ${file_path}

Get Dynamic Test Data
    [Documentation]    Get test data value by key
    [Arguments]        ${key}
    ${value}=          Get Test Data    ${key}
    Log                Retrieved test data - ${key}: ${value}
    [Return]           ${value}

Generate Test Email
    [Documentation]    Generate random email for testing
    ${email}=          Generate Random Email
    Log                Generated test email: ${email}
    [Return]           ${email}

Generate Test String
    [Documentation]    Generate random string of specified length
    [Arguments]        ${length}=10
    ${string}=         Generate Random String    ${length}
    Log                Generated test string: ${string}
    [Return]           ${string}

# Verification Keywords
Element Should Be Visible
    [Documentation]    Verify element is visible on page
    [Arguments]        ${locator}
    ${element}=        Wait For Element    ${locator}
    Log                Element is visible: ${locator}

Element Should Contain Text
    [Documentation]    Verify element contains expected text
    [Arguments]        ${locator}    ${expected_text}
    ${result}=         Verify Element Text    ${locator}    ${expected_text}
    Should Be True     ${result}
    Log                Element contains expected text: ${expected_text}

# Screenshot Keywords
Capture Screenshot
    [Documentation]    Take screenshot with timestamp
    [Arguments]        ${name}=screenshot
    ${timestamp}=      Get Current Date    result_format=%Y%m%d_%H%M%S
    ${filename}=       Set Variable    ${SCREENSHOTS_DIR}/${name}_${timestamp}.png
    Take Screenshot    ${filename}
    Log                Screenshot captured: ${filename}

Capture Screenshot On Failure
    [Documentation]    Take screenshot when test fails
    Run Keyword If Test Failed    Capture Screenshot    failure

# Advanced Web Actions
Scroll To Element
    [Documentation]    Scroll to element using JavaScript
    [Arguments]        ${locator}
    ${element}=        Wait For Element    ${locator}
    Execute JavaScript    arguments[0].scrollIntoView(true);    ARGUMENTS    ${element}
    Log                Scrolled to element: ${locator}

Wait For Element And Click
    [Documentation]    Wait for element to be clickable and click it
    [Arguments]        ${locator}    ${timeout}=${TIMEOUT}
    Wait For Element   ${locator}
    Click Element      ${locator}
    Log                Clicked element after wait: ${locator}

Enter Text And Verify
    [Documentation]    Enter text and verify it was entered correctly
    [Arguments]        ${locator}    ${text}
    Enter Text         ${locator}    ${text}
    ${entered_text}=   Get Element Text    ${locator}
    Should Be Equal    ${entered_text}    ${text}
    Log                Text entered and verified: ${text}

# Window Management
Switch To New Window
    [Documentation]    Switch to newly opened window
    [Arguments]        ${window_title}
    Switch To Window By Title    ${window_title}
    Log                Switched to window: ${window_title}

# Form Handling
Fill Login Form
    [Documentation]    Fill complete login form
    [Arguments]        ${username}    ${password}
    Enter Text         ${USERNAME_FIELD}    ${username}
    Enter Text         ${PASSWORD_FIELD}    ${password}
    Log                Login form filled for user: ${username}

Submit Form And Wait
    [Documentation]    Submit form and wait for page load
    [Arguments]        ${submit_button_locator}
    Click Element      ${submit_button_locator}
    Wait For Page Load
    Log                Form submitted and page loaded

# Test Setup and Teardown
Setup Test Environment
    [Documentation]    Setup test environment with browser and test data
    [Arguments]        ${browser}=${BROWSER}    ${test_data_file}=${EMPTY}
    Setup Browser      ${browser}
    Run Keyword If     '${test_data_file}' != '${EMPTY}'    Load Test Data From JSON    ${test_data_file}
    Log                Test environment setup complete

Cleanup Test Environment
    [Documentation]    Cleanup test environment
    Capture Screenshot On Failure
    Teardown Browser
    Log                Test environment cleanup complete
