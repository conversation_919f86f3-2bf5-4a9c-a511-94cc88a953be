*** Settings ***
Documentation    Web automation tests using Robot Framework + Java integration
Library          com.automation.keywords.WebAutomationLibrary
Library          com.automation.utils.TestDataManager
Resource         keywords/web_keywords.robot
Resource         ../resources/variables.robot

Suite Setup      Setup Test Environment    ${BROWSER}
Suite Teardown   Cleanup Test Environment
Test Setup       Capture Screenshot    test_start
Test Teardown    Capture Screenshot On Failure

*** Variables ***
${TEST_SITE_URL}    https://the-internet.herokuapp.com

*** Test Cases ***
TC001: Verify Browser Initialization
    [Documentation]    Test basic browser initialization and navigation
    [Tags]             smoke    browser
    Navigate To Url    ${TEST_SITE_URL}
    Wait For Page Load
    ${title}=          Execute JavaScript    return document.title;
    Should Contain     ${title}    The Internet
    Log                Browser initialized successfully with title: ${title}

TC002: Test Basic Element Interactions
    [Documentation]    Test basic web element interactions
    [Tags]             smoke    elements
    Navigate To Url    ${TEST_SITE_URL}/login
    Wait For Page Load
    
    # Test element visibility
    Element Should Be Visible    id=username
    Element Should Be Visible    id=password
    Element Should Be Visible    css=button[type='submit']
    
    # Test text input
    Enter Text         id=username    tomsmith
    Enter Text         id=password    SuperSecretPassword!
    
    # Test click action
    Click Element      css=button[type='submit']
    Wait For Page Load
    
    # Verify success message
    Element Should Be Visible    css=.flash.success
    ${success_msg}=    Get Element Text    css=.flash.success
    Should Contain     ${success_msg}    You logged into a secure area!
    Log                Login test completed successfully

TC003: Test Invalid Login Scenario
    [Documentation]    Test login with invalid credentials
    [Tags]             negative    login
    Navigate To Url    ${TEST_SITE_URL}/login
    Wait For Page Load
    
    Enter Text         id=username    invaliduser
    Enter Text         id=password    invalidpassword
    Click Element      css=button[type='submit']
    Wait For Page Load
    
    # Verify error message
    Element Should Be Visible    css=.flash.error
    ${error_msg}=      Get Element Text    css=.flash.error
    Should Contain     ${error_msg}    Your username is invalid!
    Log                Invalid login test completed successfully

TC004: Test Dynamic Content Handling
    [Documentation]    Test handling of dynamic content
    [Tags]             dynamic    content
    Navigate To Url    ${TEST_SITE_URL}/dynamic_content
    Wait For Page Load
    
    # Get initial content
    ${initial_text}=   Get Element Text    xpath=//div[@id='content']//div[1]//div[2]
    Log                Initial content: ${initial_text}
    
    # Click refresh button
    Click Element      xpath=//a[contains(text(),'click here')]
    Wait For Page Load
    
    # Get new content
    ${new_text}=       Get Element Text    xpath=//div[@id='content']//div[1]//div[2]
    Log                New content: ${new_text}
    
    # Content should be different (dynamic)
    Should Not Be Equal    ${initial_text}    ${new_text}
    Log                Dynamic content test completed successfully

TC005: Test Dropdown Interactions
    [Documentation]    Test dropdown selection functionality
    [Tags]             dropdown    forms
    Navigate To Url    ${TEST_SITE_URL}/dropdown
    Wait For Page Load
    
    # Test dropdown selection
    Select Dropdown By Text    id=dropdown    Option 1
    ${selected_text}=  Execute JavaScript    return document.getElementById('dropdown').value;
    Should Be Equal    ${selected_text}    1
    
    Select Dropdown By Text    id=dropdown    Option 2
    ${selected_text}=  Execute JavaScript    return document.getElementById('dropdown').value;
    Should Be Equal    ${selected_text}    2
    
    Log                Dropdown test completed successfully

TC006: Test JavaScript Execution
    [Documentation]    Test JavaScript execution capabilities
    [Tags]             javascript    advanced
    Navigate To Url    ${TEST_SITE_URL}
    Wait For Page Load
    
    # Execute JavaScript to get page information
    ${page_title}=     Execute JavaScript    return document.title;
    ${page_url}=       Execute JavaScript    return window.location.href;
    ${page_height}=    Execute JavaScript    return document.body.scrollHeight;
    
    Log                Page Title: ${page_title}
    Log                Page URL: ${page_url}
    Log                Page Height: ${page_height}
    
    # Verify JavaScript execution worked
    Should Not Be Empty    ${page_title}
    Should Contain         ${page_url}    herokuapp.com
    Should Be True         ${page_height} > 0
    
    Log                JavaScript execution test completed successfully

TC007: Test Screenshot Functionality
    [Documentation]    Test screenshot capture functionality
    [Tags]             screenshot    utility
    Navigate To Url    ${TEST_SITE_URL}
    Wait For Page Load
    
    # Take screenshot with custom name
    Capture Screenshot    homepage_test
    
    # Navigate to another page and take screenshot
    Navigate To Url    ${TEST_SITE_URL}/login
    Wait For Page Load
    Capture Screenshot    login_page_test
    
    Log                Screenshot functionality test completed successfully

TC008: Test Data Generation
    [Documentation]    Test Java utility methods for data generation
    [Tags]             data    utility
    
    # Generate random data using Java utilities
    ${random_email}=   Generate Random Email
    ${random_string}=  Generate Random String    15
    ${random_number}=  Generate Random Number    1    100
    
    Log                Generated Email: ${random_email}
    Log                Generated String: ${random_string}
    Log                Generated Number: ${random_number}
    
    # Validate generated data
    ${is_valid_email}= Is Valid Email    ${random_email}
    Should Be True     ${is_valid_email}
    
    ${string_length}=  Get Length    ${random_string}
    Should Be Equal As Numbers    ${string_length}    15
    
    Should Be True     ${random_number} >= 1 and ${random_number} <= 100
    
    Log                Data generation test completed successfully

TC009: Test Window Management
    [Documentation]    Test window switching functionality
    [Tags]             windows    navigation
    Navigate To Url    ${TEST_SITE_URL}/windows
    Wait For Page Load
    
    # Click link to open new window
    Click Element      xpath=//a[contains(text(),'Click Here')]
    
    # Switch to new window
    Switch To Window By Title    New Window
    
    # Verify we're in the new window
    ${new_window_text}=    Get Element Text    xpath=//h3
    Should Contain         ${new_window_text}    New Window
    
    Log                Window management test completed successfully

TC010: Test Complex Form Handling
    [Documentation]    Test complex form interactions
    [Tags]             forms    complex
    Navigate To Url    ${TEST_SITE_URL}/checkboxes
    Wait For Page Load
    
    # Handle checkboxes
    Click Element      xpath=//input[@type='checkbox'][1]
    Click Element      xpath=//input[@type='checkbox'][2]
    
    # Verify checkbox states using JavaScript
    ${checkbox1_checked}=  Execute JavaScript    return document.querySelectorAll('input[type="checkbox"]')[0].checked;
    ${checkbox2_checked}=  Execute JavaScript    return document.querySelectorAll('input[type="checkbox"]')[1].checked;
    
    Should Be True     ${checkbox1_checked}
    Should Be True     ${checkbox2_checked}
    
    Log                Complex form handling test completed successfully
