package com.automation.keywords;

import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.By;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.firefox.FirefoxDriver;
import org.openqa.selenium.edge.EdgeDriver;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.Select;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.TakesScreenshot;
import org.openqa.selenium.OutputType;
import io.github.bonigarcia.wdm.WebDriverManager;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.IOException;
import java.time.Duration;
import java.util.List;
import java.util.Set;

/**
 * Custom Java library for Robot Framework web automation
 * This class provides advanced web automation keywords that can be called from Robot Framework
 */
public class WebAutomationLibrary {
    
    public static final String ROBOT_LIBRARY_SCOPE = "GLOBAL";
    public static final String ROBOT_LIBRARY_VERSION = "1.0";
    
    private WebDriver driver;
    private WebDriverWait wait;
    private static final int DEFAULT_TIMEOUT = 10;
    
    /**
     * Initialize browser with specified browser type
     * @param browserName - chrome, firefox, edge
     */
    public void initializeBrowser(String browserName) {
        browserName = browserName.toLowerCase();
        
        switch (browserName) {
            case "chrome":
                WebDriverManager.chromedriver().setup();
                driver = new ChromeDriver();
                break;
            case "firefox":
                WebDriverManager.firefoxdriver().setup();
                driver = new FirefoxDriver();
                break;
            case "edge":
                WebDriverManager.edgedriver().setup();
                driver = new EdgeDriver();
                break;
            default:
                throw new IllegalArgumentException("Unsupported browser: " + browserName);
        }
        
        driver.manage().window().maximize();
        driver.manage().timeouts().implicitlyWait(Duration.ofSeconds(DEFAULT_TIMEOUT));
        wait = new WebDriverWait(driver, Duration.ofSeconds(DEFAULT_TIMEOUT));
        
        System.out.println("Browser initialized: " + browserName);
    }
    
    /**
     * Navigate to specified URL
     */
    public void navigateToUrl(String url) {
        if (driver == null) {
            throw new RuntimeException("Browser not initialized. Call 'Initialize Browser' first.");
        }
        driver.get(url);
        System.out.println("Navigated to: " + url);
    }
    
    /**
     * Wait for element to be visible and return it
     */
    public WebElement waitForElement(String locator) {
        By by = getByLocator(locator);
        return wait.until(ExpectedConditions.visibilityOfElementLocated(by));
    }
    
    /**
     * Click element with wait
     */
    public void clickElement(String locator) {
        WebElement element = waitForElement(locator);
        element.click();
        System.out.println("Clicked element: " + locator);
    }
    
    /**
     * Enter text in input field
     */
    public void enterText(String locator, String text) {
        WebElement element = waitForElement(locator);
        element.clear();
        element.sendKeys(text);
        System.out.println("Entered text '" + text + "' in element: " + locator);
    }
    
    /**
     * Get text from element
     */
    public String getElementText(String locator) {
        WebElement element = waitForElement(locator);
        String text = element.getText();
        System.out.println("Retrieved text '" + text + "' from element: " + locator);
        return text;
    }
    
    /**
     * Verify element contains expected text
     */
    public boolean verifyElementText(String locator, String expectedText) {
        String actualText = getElementText(locator);
        boolean result = actualText.contains(expectedText);
        System.out.println("Text verification - Expected: '" + expectedText + 
                          "', Actual: '" + actualText + "', Result: " + result);
        return result;
    }
    
    /**
     * Select dropdown option by visible text
     */
    public void selectDropdownByText(String locator, String optionText) {
        WebElement element = waitForElement(locator);
        Select select = new Select(element);
        select.selectByVisibleText(optionText);
        System.out.println("Selected dropdown option: " + optionText);
    }
    
    /**
     * Execute JavaScript
     */
    public Object executeJavaScript(String script) {
        JavascriptExecutor js = (JavascriptExecutor) driver;
        return js.executeScript(script);
    }
    
    /**
     * Take screenshot and save to specified path
     */
    public void takeScreenshot(String filePath) {
        try {
            TakesScreenshot screenshot = (TakesScreenshot) driver;
            File sourceFile = screenshot.getScreenshotAs(OutputType.FILE);
            File destFile = new File(filePath);
            FileUtils.copyFile(sourceFile, destFile);
            System.out.println("Screenshot saved: " + filePath);
        } catch (IOException e) {
            throw new RuntimeException("Failed to take screenshot: " + e.getMessage());
        }
    }
    
    /**
     * Switch to window by title
     */
    public void switchToWindowByTitle(String windowTitle) {
        Set<String> windowHandles = driver.getWindowHandles();
        for (String handle : windowHandles) {
            driver.switchTo().window(handle);
            if (driver.getTitle().contains(windowTitle)) {
                System.out.println("Switched to window: " + windowTitle);
                return;
            }
        }
        throw new RuntimeException("Window with title '" + windowTitle + "' not found");
    }
    
    /**
     * Wait for page to load completely
     */
    public void waitForPageLoad() {
        wait.until(webDriver -> 
            ((JavascriptExecutor) webDriver).executeScript("return document.readyState").equals("complete"));
        System.out.println("Page loaded completely");
    }
    
    /**
     * Close browser
     */
    public void closeBrowser() {
        if (driver != null) {
            driver.quit();
            driver = null;
            System.out.println("Browser closed");
        }
    }
    
    /**
     * Helper method to convert string locator to By object
     */
    private By getByLocator(String locator) {
        if (locator.startsWith("id=")) {
            return By.id(locator.substring(3));
        } else if (locator.startsWith("name=")) {
            return By.name(locator.substring(5));
        } else if (locator.startsWith("class=")) {
            return By.className(locator.substring(6));
        } else if (locator.startsWith("css=")) {
            return By.cssSelector(locator.substring(4));
        } else if (locator.startsWith("xpath=")) {
            return By.xpath(locator.substring(6));
        } else if (locator.startsWith("//")) {
            return By.xpath(locator);
        } else {
            // Default to ID if no prefix specified
            return By.id(locator);
        }
    }
}
