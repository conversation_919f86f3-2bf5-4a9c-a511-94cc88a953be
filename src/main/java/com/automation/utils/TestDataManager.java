package com.automation.utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.io.FileInputStream;

/**
 * Utility class for managing test data
 * Can be used from Robot Framework to handle complex data operations
 */
public class TestDataManager {
    
    private static final String ROBOT_LIBRARY_SCOPE = "GLOBAL";
    private ObjectMapper objectMapper;
    private Map<String, Object> testData;
    
    public TestDataManager() {
        this.objectMapper = new ObjectMapper();
        this.testData = new HashMap<>();
    }
    
    /**
     * Load test data from JSON file
     */
    public void loadJsonTestData(String filePath) {
        try {
            File jsonFile = new File(filePath);
            JsonNode rootNode = objectMapper.readTree(jsonFile);
            
            // Convert JSON to Map for easy access
            testData = objectMapper.convertValue(rootNode, Map.class);
            System.out.println("Loaded test data from: " + filePath);
            
        } catch (IOException e) {
            throw new RuntimeException("Failed to load JSON test data: " + e.getMessage());
        }
    }
    
    /**
     * Load test data from properties file
     */
    public void loadPropertiesTestData(String filePath) {
        try {
            Properties props = new Properties();
            FileInputStream fis = new FileInputStream(filePath);
            props.load(fis);
            fis.close();
            
            // Convert Properties to Map
            for (String key : props.stringPropertyNames()) {
                testData.put(key, props.getProperty(key));
            }
            System.out.println("Loaded test data from properties: " + filePath);
            
        } catch (IOException e) {
            throw new RuntimeException("Failed to load properties test data: " + e.getMessage());
        }
    }
    
    /**
     * Get test data value by key
     */
    public String getTestData(String key) {
        Object value = testData.get(key);
        if (value == null) {
            throw new RuntimeException("Test data key not found: " + key);
        }
        return value.toString();
    }
    
    /**
     * Get nested test data (for JSON objects)
     */
    public String getNestedTestData(String parentKey, String childKey) {
        Object parent = testData.get(parentKey);
        if (parent instanceof Map) {
            Map<String, Object> parentMap = (Map<String, Object>) parent;
            Object value = parentMap.get(childKey);
            if (value != null) {
                return value.toString();
            }
        }
        throw new RuntimeException("Nested test data not found: " + parentKey + "." + childKey);
    }
    
    /**
     * Set test data value
     */
    public void setTestData(String key, String value) {
        testData.put(key, value);
        System.out.println("Set test data - " + key + ": " + value);
    }
    
    /**
     * Generate random email
     */
    public String generateRandomEmail() {
        long timestamp = System.currentTimeMillis();
        String email = "test" + timestamp + "@example.com";
        System.out.println("Generated random email: " + email);
        return email;
    }
    
    /**
     * Generate random string of specified length
     */
    public String generateRandomString(int length) {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        StringBuilder sb = new StringBuilder();
        
        for (int i = 0; i < length; i++) {
            int index = (int) (Math.random() * chars.length());
            sb.append(chars.charAt(index));
        }
        
        String randomString = sb.toString();
        System.out.println("Generated random string: " + randomString);
        return randomString;
    }
    
    /**
     * Generate random number within range
     */
    public int generateRandomNumber(int min, int max) {
        int randomNum = (int) (Math.random() * (max - min + 1)) + min;
        System.out.println("Generated random number: " + randomNum);
        return randomNum;
    }
    
    /**
     * Format string with test data placeholders
     * Example: "Hello {username}, welcome to {appname}" with placeholders
     */
    public String formatStringWithTestData(String template) {
        String result = template;
        
        for (Map.Entry<String, Object> entry : testData.entrySet()) {
            String placeholder = "{" + entry.getKey() + "}";
            if (result.contains(placeholder)) {
                result = result.replace(placeholder, entry.getValue().toString());
            }
        }
        
        System.out.println("Formatted string: " + result);
        return result;
    }
    
    /**
     * Validate email format
     */
    public boolean isValidEmail(String email) {
        String emailRegex = "^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$";
        boolean isValid = email.matches(emailRegex);
        System.out.println("Email validation for '" + email + "': " + isValid);
        return isValid;
    }
    
    /**
     * Get all test data keys
     */
    public String[] getAllTestDataKeys() {
        return testData.keySet().toArray(new String[0]);
    }
    
    /**
     * Clear all test data
     */
    public void clearTestData() {
        testData.clear();
        System.out.println("Cleared all test data");
    }
    
    /**
     * Print all test data (for debugging)
     */
    public void printAllTestData() {
        System.out.println("=== All Test Data ===");
        for (Map.Entry<String, Object> entry : testData.entrySet()) {
            System.out.println(entry.getKey() + " = " + entry.getValue());
        }
        System.out.println("====================");
    }
}
