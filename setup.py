#!/usr/bin/env python3
"""
Setup script for Robot Framework + Java Web Automation
This script helps set up the complete environment for hybrid automation testing
"""

import os
import sys
import subprocess
import platform

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        if result.stdout:
            print(f"Output: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed")
        print(f"Error: {e.stderr.strip()}")
        return False

def check_java():
    """Check if Java is installed"""
    try:
        result = subprocess.run(['java', '-version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Java is installed")
            return True
    except FileNotFoundError:
        pass
    
    print("❌ Java is not installed or not in PATH")
    print("Please install Java 11 or higher from: https://adoptium.net/")
    return False

def check_maven():
    """Check if <PERSON><PERSON> is installed"""
    try:
        result = subprocess.run(['mvn', '-version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Maven is installed")
            return True
    except FileNotFoundError:
        pass
    
    print("❌ Maven is not installed or not in PATH")
    print("Please install Maven from: https://maven.apache.org/download.cgi")
    return False

def check_python():
    """Check if Python is installed"""
    try:
        result = subprocess.run([sys.executable, '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Python is installed: {result.stdout.strip()}")
            return True
    except FileNotFoundError:
        pass
    
    print("❌ Python is not installed or not in PATH")
    return False

def install_python_dependencies():
    """Install Python dependencies"""
    dependencies = [
        'robotframework',
        'robotframework-seleniumlibrary',
        'robotframework-jython',
        'requests'
    ]
    
    for dep in dependencies:
        if not run_command(f'pip install {dep}', f'Installing {dep}'):
            return False
    
    return True

def compile_java_code():
    """Compile Java code using Maven"""
    return run_command('mvn clean compile', 'Compiling Java code')

def create_directories():
    """Create necessary directories"""
    directories = [
        'results',
        'results/screenshots',
        'drivers'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✅ Created directory: {directory}")

def download_sample_drivers():
    """Information about downloading WebDriver binaries"""
    print("\n📋 WebDriver Setup Information:")
    print("WebDriverManager in the Java code will automatically download drivers.")
    print("However, you can manually download drivers to the 'drivers' folder:")
    print("- ChromeDriver: https://chromedriver.chromium.org/")
    print("- GeckoDriver (Firefox): https://github.com/mozilla/geckodriver/releases")
    print("- EdgeDriver: https://developer.microsoft.com/en-us/microsoft-edge/tools/webdriver/")

def main():
    """Main setup function"""
    print("🚀 Setting up Robot Framework + Java Web Automation Environment")
    print("=" * 60)
    
    # Check prerequisites
    print("\n📋 Checking Prerequisites...")
    java_ok = check_java()
    maven_ok = check_maven()
    python_ok = check_python()
    
    if not all([java_ok, maven_ok, python_ok]):
        print("\n❌ Prerequisites not met. Please install missing components and try again.")
        return False
    
    # Create directories
    print("\n📁 Creating Directories...")
    create_directories()
    
    # Install Python dependencies
    print("\n🐍 Installing Python Dependencies...")
    if not install_python_dependencies():
        print("❌ Failed to install Python dependencies")
        return False
    
    # Compile Java code
    print("\n☕ Compiling Java Code...")
    if not compile_java_code():
        print("❌ Failed to compile Java code")
        return False
    
    # WebDriver information
    download_sample_drivers()
    
    print("\n🎉 Setup completed successfully!")
    print("\n📖 Next Steps:")
    print("1. Review the test files in the 'tests' directory")
    print("2. Modify variables in 'resources/variables.robot' for your application")
    print("3. Run tests using: robot tests/web_tests.robot")
    print("4. View results in the 'results' directory")
    
    print("\n🔧 Example Commands:")
    print("# Run all tests:")
    print("robot tests/web_tests.robot")
    print("\n# Run specific test:")
    print("robot -t 'TC001*' tests/web_tests.robot")
    print("\n# Run tests with specific browser:")
    print("robot -v BROWSER:firefox tests/web_tests.robot")
    print("\n# Run tests with tags:")
    print("robot -i smoke tests/web_tests.robot")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
